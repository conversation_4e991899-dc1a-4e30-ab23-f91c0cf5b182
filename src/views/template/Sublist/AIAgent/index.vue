<template>
    <div class="ai-agent">
        <div class="ai-agent__switch">
            <el-switch
                v-model="enableAgent"
                active-color="#13ce66"
                :disabled="loading"
                @change="handleSwitchChange"
            ></el-switch>
            <span>拖拽Agent</span>
        </div>
        <div class="ai-agent__description">
            <p>凡是从本地上传的文档，Agent都会自动帮你找到各个签约方的盖章处，以代替人工施动盖章处的繁琐操作</p>
            <p>*单份文档页数不能超过50页</p>
            <p>*配合"模板专用章"、"自动盖"等功能，可以实现非标合同盖章的完全自动化</p>
        </div>

        <div class="ai-agent__config" v-loading="loading">
            <h3>拖章规则配置</h3>
            <p class="config-tip">勾选需要使用的规则，并调整各个规则的排序，以明确规则冲突时的优先级。</p>
            
            <div class="rule-item">
                <el-checkbox v-model="strategies.NEED_OVERLAPPING_SEAL" :disabled="!enableAgent">需要骑缝章</el-checkbox>
                <p class="rule-desc">*如果不勾选，则不会自动添加骑缝章盖章处</p>
            </div>

            <div class="rule-item">
                <el-checkbox v-model="strategies.RECIPROCAL_SEALING" :disabled="!enableAgent">对等盖章：按已有盖章处匹配签约方的盖章处</el-checkbox>
                <p class="rule-desc">*例如，若合同上已有对方的3个印章（分布在不同页面），系统会自动为我方在相应位置也生成3个对应的盖章位置。 注：本功能仅在单方盖章时生效。</p>
            </div>

            <div class="rule-item">
                <el-checkbox v-model="strategies.SEAL_ON_LAST_LINE_OF_TEXT" :disabled="!enableAgent">文件中若没有明确的盖章处，则盖在文件最后一行的文字上</el-checkbox>
                <p class="rule-desc">*不是传统意义上的合同，没有明确的盖章处指示的文件，如内部单据等需要盖章的文件</p>
            </div>

            <div class="rule-item">
                <el-checkbox v-model="strategies.REASONABLE_AREA_SEALING" :disabled="!enableAgent">印章应放置在合理的区域（如盖章栏附近、合同末页公司信息处等）</el-checkbox>
                <p class="rule-desc">*可能会与对等盖章、每页盖章等规则有冲突，需要调整优先级，以确保优先需要满足的规则</p>
            </div>

            <div class="rule-item">
                <el-checkbox v-model="strategies.SEAL_EACH_PAGE" :disabled="!enableAgent">每页盖章</el-checkbox>
                <p class="rule-desc">*如果是对账单、招投标文件，则每页相同位置都需要盖章</p>
            </div>
        </div>

        <div class="ai-agent__adjustment">
            <h3>自助调优</h3>
            <div class="adjustment-item">
                <p>(1) 盖章位置调优（不含骑缝章）</p>
                <div class="position-adjust">
                    <div class="adjust-row">
                        <span>系统指定的盖章处需向上移动</span>
                        <el-input v-model="strategies.SEAL_MOVE_CONFIG.upMove" size="small" class="adjust-input" :disabled="!enableAgent"></el-input>
                        <span>厘米</span>
                    </div>
                    <div class="adjust-row">
                        <span>系统指定的盖章处需向左移动</span>
                        <el-input v-model="strategies.SEAL_MOVE_CONFIG.leftMove" size="small" class="adjust-input" :disabled="!enableAgent"></el-input>
                        <span>厘米</span>
                    </div>
                </div>
                <p class="adjust-tip">*可填写负数，代表反方向。</p>
                <p class="adjust-tip">*N厘米是打印后的大小，默认情况下一个章的直径约4厘米，可以以此折算</p>
                <p class="adjust-tip">*移动盖章处可能会与已有盖章处重叠，请知悉</p>
            </div>
            
            <div class="adjustment-item">
                <p>(2) 是否盖章调整</p>
                <div class="content-adjust">
                    <div class="adjust-row">
                        <span>若文件中出现</span>
                        <el-input v-model="strategies.ADD_SEAL_BY_KEYWORD.keyword" size="small" class="content-input" :disabled="!enableAgent"></el-input>
                        <span>该内容对应的位置需要盖章</span>
                    </div>
                    <div class="adjust-row">
                        <span>若文件中出现</span>
                        <el-input v-model="strategies.REMOVE_SEAL_BY_KEYWORD.keyword" size="small" class="content-input" :disabled="!enableAgent"></el-input>
                        <span>该内容对应的位置不需要盖章</span>
                    </div>
                </div>
                <p class="adjust-tip">*此处配置后，规则将变成最高优先级</p>
            </div>
        </div>

        <div class="ai-agent__actions">
            <el-button
                type="primary"
                :loading="loading"
                :disabled="!enableAgent"
                @click="saveConfig"
            >
                保存配置
            </el-button>
            <el-button @click="resetConfig">重置配置</el-button>
        </div>
    </div>
</template>

<script>
import { getStampRecommendationRule, saveStampRecommendationRule, toggleStampRecommendationSwitch } from 'src/api/template/index.js';

export default {
    name: 'AIAgent',
    props: {
        templateId: {
            type: String,
            required: true
        },
        templateName: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            enableAgent: false,
            loading: false,
            // 直接使用后端策略类型作为数据结构
            strategies: {
                NEED_OVERLAPPING_SEAL: false,           // 需要骑缝章
                RECIPROCAL_SEALING: true,               // 对等盖章
                SEAL_ON_LAST_LINE_OF_TEXT: true,        // 最后一行文字盖章
                REASONABLE_AREA_SEALING: true,          // 合理区域盖章
                SEAL_EACH_PAGE: false,                  // 每页盖章
                SEAL_MOVE_CONFIG: {                     // 位置调优
                    enabled: false,
                    upMove: '0',
                    leftMove: '0'
                },
                ADD_SEAL_BY_KEYWORD: {                  // 关键字添加印章
                    enabled: false,
                    keyword: ''
                },
                REMOVE_SEAL_BY_KEYWORD: {               // 关键字移除印章
                    enabled: false,
                    keyword: ''
                }
            }
        };
    },
    mounted() {
        this.loadConfig();
    },
    watch: {
        // 监听位置调优输入，自动设置enabled状态
        'strategies.SEAL_MOVE_CONFIG.upMove'() {
            this.updateMoveConfigEnabled();
        },
        'strategies.SEAL_MOVE_CONFIG.leftMove'() {
            this.updateMoveConfigEnabled();
        },
        // 监听关键字输入，自动设置enabled状态
        'strategies.ADD_SEAL_BY_KEYWORD.keyword'() {
            this.strategies.ADD_SEAL_BY_KEYWORD.enabled = !!this.strategies.ADD_SEAL_BY_KEYWORD.keyword.trim();
        },
        'strategies.REMOVE_SEAL_BY_KEYWORD.keyword'() {
            this.strategies.REMOVE_SEAL_BY_KEYWORD.enabled = !!this.strategies.REMOVE_SEAL_BY_KEYWORD.keyword.trim();
        }
    },
    methods: {
        // 加载配置
        async loadConfig() {
            if (!this.templateId) return;

            this.loading = true;
            try {
                const response = await getStampRecommendationRule(this.templateId);
                const data = response.data;

                // 设置开关状态
                this.enableAgent = data.useStampRecommendation || false;

                // 解析策略配置
                if (data.stampStrategies && Array.isArray(data.stampStrategies)) {
                    this.parseStrategiesFromAPI(data.stampStrategies);
                }
            } catch (error) {
                console.error('加载印章推荐配置失败:', error);
                this.$message.error('加载配置失败，请稍后重试');
            } finally {
                this.loading = false;
            }
        },

        // 解析API返回的策略配置
        parseStrategiesFromAPI(strategies) {
            // 重置所有配置
            this.resetConfig();

            strategies.forEach(strategy => {
                const { strategyType, strategyConfigParam } = strategy;

                // 直接使用策略类型作为key
                if (this.strategies.hasOwnProperty(strategyType)) {
                    if (typeof this.strategies[strategyType] === 'boolean') {
                        // 简单布尔类型策略
                        this.strategies[strategyType] = true;
                    } else if (typeof this.strategies[strategyType] === 'object') {
                        // 复杂对象类型策略
                        this.strategies[strategyType].enabled = true;

                        if (strategyConfigParam) {
                            switch (strategyType) {
                                case 'SEAL_MOVE_CONFIG':
                                    this.strategies[strategyType].upMove = strategyConfigParam.upMove || '0';
                                    this.strategies[strategyType].leftMove = strategyConfigParam.leftMove || '0';
                                    break;
                                case 'ADD_SEAL_BY_KEYWORD':
                                case 'REMOVE_SEAL_BY_KEYWORD':
                                    this.strategies[strategyType].keyword = strategyConfigParam.keyword || '';
                                    break;
                            }
                        }
                    }
                }
            });
        },

        // 重置配置
        resetConfig() {
            this.strategies = {
                NEED_OVERLAPPING_SEAL: false,
                RECIPROCAL_SEALING: false,
                SEAL_ON_LAST_LINE_OF_TEXT: false,
                REASONABLE_AREA_SEALING: false,
                SEAL_EACH_PAGE: false,
                SEAL_MOVE_CONFIG: {
                    enabled: false,
                    upMove: '0',
                    leftMove: '0'
                },
                ADD_SEAL_BY_KEYWORD: {
                    enabled: false,
                    keyword: ''
                },
                REMOVE_SEAL_BY_KEYWORD: {
                    enabled: false,
                    keyword: ''
                }
            };
        },

        // 保存配置
        async saveConfig() {
            if (!this.templateId) {
                this.$message.error('模板ID不能为空');
                return;
            }

            this.loading = true;
            try {
                const ruleConfig = this.buildRuleConfig();
                await saveStampRecommendationRule(this.templateId, ruleConfig);
                this.$message.success('保存成功');
            } catch (error) {
                console.error('保存印章推荐配置失败:', error);
                this.$message.error('保存失败，请稍后重试');
            } finally {
                this.loading = false;
            }
        },

        // 构建规则配置对象
        buildRuleConfig() {
            const stampStrategies = [];
            let order = 1;

            // 遍历所有策略
            Object.keys(this.strategies).forEach(strategyType => {
                const strategy = this.strategies[strategyType];

                if (typeof strategy === 'boolean' && strategy) {
                    // 简单布尔类型策略
                    stampStrategies.push({
                        strategyType,
                        order: order++,
                        strategyConfigParam: null
                    });
                } else if (typeof strategy === 'object' && strategy.enabled) {
                    // 复杂对象类型策略
                    let strategyConfigParam = null;

                    switch (strategyType) {
                        case 'SEAL_MOVE_CONFIG':
                            const upMove = parseFloat(strategy.upMove) || 0;
                            const leftMove = parseFloat(strategy.leftMove) || 0;
                            if (upMove !== 0 || leftMove !== 0) {
                                strategyConfigParam = {
                                    upMove: upMove.toString(),
                                    leftMove: leftMove.toString()
                                };
                            }
                            break;
                        case 'ADD_SEAL_BY_KEYWORD':
                        case 'REMOVE_SEAL_BY_KEYWORD':
                            if (strategy.keyword && strategy.keyword.trim()) {
                                strategyConfigParam = {
                                    keyword: strategy.keyword.trim()
                                };
                            }
                            break;
                    }

                    // 只有在有有效配置时才添加策略
                    if (strategyConfigParam || strategyType === 'SEAL_MOVE_CONFIG') {
                        stampStrategies.push({
                            strategyType,
                            order: order++,
                            strategyConfigParam
                        });
                    }
                }
            });

            return {
                useStampRecommendation: this.enableAgent,
                stampStrategies
            };
        },

        // 更新位置调优enabled状态
        updateMoveConfigEnabled() {
            const upMove = parseFloat(this.strategies.SEAL_MOVE_CONFIG.upMove) || 0;
            const leftMove = parseFloat(this.strategies.SEAL_MOVE_CONFIG.leftMove) || 0;
            this.strategies.SEAL_MOVE_CONFIG.enabled = upMove !== 0 || leftMove !== 0;
        },

        // 处理开关切换
        async handleSwitchChange(value) {
            if (!this.templateId) {
                this.$message.error('模板ID不能为空');
                this.enableAgent = !value; // 回滚状态
                return;
            }

            this.loading = true;
            try {
                await toggleStampRecommendationSwitch(this.templateId, value);
                this.$message.success(value ? '已开启印章推荐功能' : '已关闭印章推荐功能');
            } catch (error) {
                console.error('切换印章推荐开关失败:', error);
                this.$message.error('操作失败，请稍后重试');
                this.enableAgent = !value; // 回滚状态
            } finally {
                this.loading = false;
            }
        }
    }
};
</script>

<style lang="scss">
.ai-agent {
    padding: 20px;
    
    &__switch {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        
        span {
            margin-left: 10px;
            font-size: 14px;
        }
    }
    
    &__description {
        background-color: #f8f8f8;
        padding: 15px;
        border-radius: 4px;
        margin-bottom: 20px;
        
        p {
            margin: 5px 0;
            font-size: 13px;
            line-height: 1.5;
        }
    }
    
    &__config {
        margin-bottom: 30px;
        
        h3 {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .config-tip {
            font-size: 13px;
            color: #666;
            margin-bottom: 15px;
        }
    }
    
    .rule-item {
        margin-bottom: 15px;
        
        .rule-desc {
            margin-top: 5px;
            margin-left: 24px;
            font-size: 12px;
            color: #999;
        }
    }
    
    &__adjustment {
        h3 {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .adjustment-item {
            margin-bottom: 20px;
            
            p {
                margin-bottom: 10px;
            }
            
            .position-adjust, .content-adjust {
                margin-left: 20px;
                margin-bottom: 10px;
            }
            
            .adjust-row {
                display: flex;
                align-items: center;
                margin-bottom: 10px;
                
                span {
                    font-size: 13px;
                }
            }
            
            .adjust-input {
                width: 80px;
                margin: 0 10px;
            }
            
            .content-input {
                width: 300px;
                margin: 0 10px;
            }
            
            .adjust-tip {
                font-size: 12px;
                color: #999;
                margin-left: 20px;
            }
        }
    }

    &__actions {
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #eee;
        text-align: center;

        .el-button {
            margin: 0 10px;
        }
    }
}
</style>